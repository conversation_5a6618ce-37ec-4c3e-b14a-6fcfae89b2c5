# Currency Exchange Service
The library provides services for currency exchange using SWS internal service or default static rates

## Important information!!!
This repository has two branches:
- develop
- release/v2

The 'develop' branch contains the old pieces of code, old dependencies and uses nodejs <= 14.
If you want to fix something, pls create a branch from develop and merge PR in the develop branch changing the version in package.json.
You can use any version from 1.0.0 to 2.0.0

The 'release/v2' branch is intended for the transition to new technologies. The version will start from 2.0.0 in package.json

## Local Development
This project is using TypeScript 5+.
```sh
$ git clone sw-currency-exchange
$ cd sw-currency-exchange
$ npm install
$ npm test
```

## Environment variables

| Variable                                | Description                                                                                              |
|-----------------------------------------|----------------------------------------------------------------------------------------------------------|
|`CURRENCY_RATES_BASE_CURRENCIES`         | List of base currencies to fetch crosses for (default: EUR,USD,CNY,KRW,MYR)                              |
|`CURRENCY_RATES_BASE_DEFAULT`            | Default base currency to use if from/to currencies are not in the list of base currencies (default: USD) |
|`CURRENCY_RATES_PROVIDER`                | Currency rates provider: "default" or "sws"                                                              |
|`CURRENCY_RATES_UPDATE_SCHEDULE`         | Cron schedule when fetch new currency rates from provider (default: 30 23 * * *)                         |
|`CURRENCY_RATES_UPDATE_TIMEOUT_MSEC`     | Timeout to try to get data (default: 1 min)                                                              |
|`CURRENCY_RATES_LOCK_TTL_SEC`            | Time to acquire lock to fetch currency rates from provider and update them in database (default: 30 sec) |
|`CURRENCY_RATES_LOCK_RETRY_TIMEOUT_MSEC` | Timeout to wait for lock to be released (default: 1 sec)                                                 |
|`CURRENCY_RATES_DB_RATES_TTL_SEC`        | Time to expire rates in database (default: 2 days)                                                       |
|`CURRENCY_RATES_DB_KEY_PREFIX`           | Prefix to key where to store rates in database (default: "currency:exchange:rate" )                      |

SWS provider

| Variable                           | Description                                    |
|------------------------------------|------------------------------------------------|
|`SWS_CURRENCY_EXCHANGE_API_URL`     | Base URL for SWS currency exchange API        |
|`INTERNAL_SERVER_TOKEN_EXPIRES_IN`  | Token expiration time in seconds (default: 300) |
|`INTERNAL_SERVER_TOKEN_ALGORITHM`   | Token algorithm (default: HS256)              |
|`INTERNAL_SERVER_TOKEN_ISSUER`      | Token issuer (default: skywindgroup)          |
|`INTERNAL_SERVER_TOKEN_SECRET`      | Secret for token generation                   |
