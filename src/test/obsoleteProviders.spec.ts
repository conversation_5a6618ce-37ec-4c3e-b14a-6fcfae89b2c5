import { expect } from "chai";
import { SWSCurrencyProvider } from "../skywind/providers/swsCurrencyProvider";
import { DefaultCurrencyProvider } from "../skywind/providers/defaultCurrencyProvider";
import { CurrencyProvider, ExchangeRateProvider } from "../skywind/types";

// Mock the getCurrencyProvider function to test error handling
function getCurrencyProviderWithMockConfig(provider: string): CurrencyProvider {
    switch (provider) {
        case ExchangeRateProvider.OXR:
            throw new Error("OXR provider is no longer supported. Please use 'sws' or 'default' provider instead.");
        case ExchangeRateProvider.OANDA:
            throw new Error("OANDA provider is no longer supported. Please use 'sws' or 'default' provider instead.");
        case ExchangeRateProvider.SWS:
            return new SWSCurrencyProvider("http://localhost:3030", {});
        default:
            return new DefaultCurrencyProvider();
    }
}

describe("Obsolete Providers", () => {
    it("should throw error for OXR provider", () => {
        expect(() => getCurrencyProviderWithMockConfig(ExchangeRateProvider.OXR))
            .to.throw("OXR provider is no longer supported. Please use 'sws' or 'default' provider instead.");
    });

    it("should throw error for OANDA provider", () => {
        expect(() => getCurrencyProviderWithMockConfig(ExchangeRateProvider.OANDA))
            .to.throw("OANDA provider is no longer supported. Please use 'sws' or 'default' provider instead.");
    });

    it("should return SWS provider for sws config", () => {
        const provider = getCurrencyProviderWithMockConfig(ExchangeRateProvider.SWS);
        expect(provider).to.be.instanceOf(SWSCurrencyProvider);
    });

    it("should return default provider for default config", () => {
        const provider = getCurrencyProviderWithMockConfig(ExchangeRateProvider.DEFAULT);
        expect(provider).to.be.instanceOf(DefaultCurrencyProvider);
    });
});
