import { expect } from "chai";
import { getCurrencyProvider } from "../index";
import { DefaultCurrencyProvider } from "../skywind/providers/defaultCurrencyProvider";

describe("Integration Tests", () => {
    it("should return default provider when no provider is specified", () => {
        // The default config should return DefaultCurrencyProvider
        const provider = getCurrencyProvider();
        expect(provider).to.be.instanceOf(DefaultCurrencyProvider);
    });
});
