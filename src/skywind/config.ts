export interface CurrencyExchangeOptions {
    sws?: {
        baseUrl: string;
        internalServerToken: any;
    };
    defaultBaseCurrency: string;
    baseCurrencies: string[];
    provider: string;
    updateSchedule: string;
    failedUpdateTimeout: number;
    lockTtl: number;
    lockRetryTimeout: number;
    dbRatesTtl: number;
    dbKeyPrefix: string;
}

export function sanitizeSensitiveData(options: any,
                                      sensitiveKeys: string[] = ["appKey", "apiKey", "secret"]): any {
    const newOptions = { ...options };
    for (const key of Object.keys(newOptions)) {
        const value = newOptions[key];

        if (sensitiveKeys.includes(key) && typeof value === "string") {
            newOptions[key] = `${value.substring(0, 5)}####`;
        }

        if (typeof value === "object" && !Array.isArray(value)) {
            newOptions[key] = sanitizeSensitiveData(newOptions[key]);
        }
    }

    return newOptions;

}

const config: CurrencyExchangeOptions = {

    sws: {
        baseUrl: process.env.SWS_CURRENCY_EXCHANGE_API_URL || "http://localhost:3030",

        internalServerToken: {
            expiresIn: +process.env.INTERNAL_SERVER_TOKEN_EXPIRES_IN || 300,
            algorithm: process.env.INTERNAL_SERVER_TOKEN_ALGORITHM || "HS256",
            issuer: process.env.INTERNAL_SERVER_TOKEN_ISSUER || "skywindgroup",
            secret: process.env.INTERNAL_SERVER_TOKEN_SECRET || "TU8N9oP4pPfrUMaRYkjwBsOyw0hgg39sPsTjONrgnN1ErJbn2"
        },
    },

    /**
     * List of base currencies to fetch crosses for
     */
    baseCurrencies: (process.env.CURRENCY_RATES_BASE_CURRENCIES || "EUR,USD,CNY,KRW,MYR").split(","),

    /**
     * Default base currency to use if from/to currencies are not in the list of base currencies
     */
    defaultBaseCurrency: process.env.CURRENCY_RATES_BASE_DEFAULT || "USD",

    /**
     * Currency rates provider: "default" or "sws"
     */
    provider: process.env.CURRENCY_RATES_PROVIDER || "default",

    /**
     * Cron schedule when fetch new currency rates from provider
     */
    updateSchedule: process.env.CURRENCY_RATES_UPDATE_SCHEDULE || "0 2 * * *",

    /**
     * timeout to try to get data
     */
    failedUpdateTimeout: +process.env.CURRENCY_RATES_UPDATE_TIMEOUT_MSEC || (60 * 1000),

    /**
     * Time to acquire lock to fetch currency rates from provider and update them in database.
     */
    lockTtl: +process.env.CURRENCY_RATES_LOCK_TTL_SEC || 30,

    /**
     * Timeout to wait for lock to be released.
     */
    lockRetryTimeout: +process.env.CURRENCY_RATES_LOCK_RETRY_TIMEOUT_MSEC || 1000,

    /**
     * Time to expire rates in database (2 days)
     */
    dbRatesTtl: +process.env.CURRENCY_RATES_DB_RATES_TTL_SEC || (2 * 24 * 60 * 60),

    /**
     * prefix to key where to store rates in database
     */
    dbKeyPrefix: process.env.CURRENCY_RATES_DB_KEY_PREFIX || "currency:exchange:rate",
};

export default config;
