export class CurrencyRatesUnavailableError extends Error {
    private causeError;
    constructor(cause?: Error | string) {
        super("Currency exchange server is unavailable");
        this.causeError = cause instanceof Error ? cause : new Error(cause);
    }

    public cause() {
        return this.causeError;
    }
}

export class CurrencyNotFoundError extends Error {
    public readonly currency: string;

    constructor(currency: string) {
        super(`Currency not found : ${currency}`);
        this.currency = currency;
    }
}

export class VirtualCurrencyRateError extends Error {
    constructor() {
        super("Virtual currency exchange rate is missing or not valid");
    }
}

export class CurrencyRatesOutdatedError extends Error {
    constructor() {
        super("Currency rates are outdated");
    }
}

export enum ExchangeRateProvider {
    SWS = "sws",
    OANDA = "oanda",
    OXR = "oxr",
    DEFAULT = "default"
}

export enum ExchangeRateType {
    BID = "bid",
    ASK = "ask"
}

export interface ExchangeRate {
    provider: ExchangeRateProvider;
    type: ExchangeRateType;
    ts: string;
    startTime: number;
    endTime: number;
    rates: CustomExchangeRate;
}

export interface CurrencyRate {
    [currencyCode: string]: number; // ex: "EUR": 1.343542
}

export interface CustomExchangeRate {
    [base: string]: CurrencyRate;
}

export interface CurrencyProvider {
    getRates(date: Date, type: ExchangeRateType): Promise<ExchangeRate>;
}

export interface GameLimitsCurrency {
    currency?: string;
    toEURMultiplier?: number;
    copyLimitsFrom?: string;
    version?: number;
}

/**
 * Service for currency exchange based on "Open Exchange Rates".
 */
export interface CurrencyExchange {
    /**
     * Convert amount according to rates
     * @param amount - amount to convert
     * @param baseCurrency - currency to convert from
     * @param targetCurrency - currency to convert to
     * @param currencyRate - (optional) custom currency exchange rates
     */
    exchange(amount: number,
             baseCurrency: string,
             targetCurrency: string,
             currencyRate?: CustomExchangeRate): number;

    /**
     * Get currency rate for conversion from base to target currency
     * @param baseCurrency - currency to convert from
     * @param targetCurrency - currency to convert to
     * @param currencyRate - (optional) currency exchange rates
     */
    getExchangeRate(baseCurrency: string,
                    targetCurrency: string,
                    currencyRate?: CustomExchangeRate): number;

    /**
     * Convert amount according to specified exchangeRate. Normalized by targetCurrency multiplier
     * @param amount - amount (in source currency) to convert
     * @param exchangeRate - Ratio between targetCurrency and source currency, i.e.
     *        The price in targetCurrency for 1 unit of source currency. (the same as getExchangeRate() method return)
     * @param targetCurrency - currency(code) to convert to
     */
    exchangeWithRate(amount: number, exchangeRate: number, targetCurrency: string): number;

    /**
     * Return List of exchanges rates
     */
    getExchangeRatesList(): ExchangeRate;
}
