import config from "./skywind/config";
import { SWSCurrencyProvider } from "./skywind/providers/swsCurrencyProvider";
import { DefaultCurrencyProvider } from "./skywind/providers/defaultCurrencyProvider";
import { CurrencyExchangeService } from "./skywind/currencyExchangeService";
import { CurrencyExchange, CurrencyProvider, ExchangeRateProvider, ExchangeRateType } from "./skywind/types";
import { RedisPool } from "./skywind/redisTypes";

export * from "./skywind/types";
export * from "./skywind/redisTypes";
export * from "./skywind/currencies";
export * from "./skywind/externalCurrencyReplacement";
export * from "./skywind/gameLimitsCurrencies";

export function getCurrencyProvider(): CurrencyProvider {
    switch (config.provider) {
        case ExchangeRateProvider.OXR:
            throw new Error("OXR provider is no longer supported. Please use 'sws' or 'default' provider instead.");
        case ExchangeRateProvider.OANDA:
            throw new Error("OANDA provider is no longer supported. Please use 'sws' or 'default' provider instead.");
        case ExchangeRateProvider.SWS:
            return new SWSCurrencyProvider(config.sws.baseUrl, config.sws.internalServerToken);
        default:
            return new DefaultCurrencyProvider();
    }
}

export async function createCurrencyExchange(pool: RedisPool, type = ExchangeRateType.BID): Promise<CurrencyExchange> {
    const provider = getCurrencyProvider();
    const service = new CurrencyExchangeService();
    await service.init(pool, provider, config, type);
    return service;
}
